# frozen_string_literal: true

#
# Copyright (C) 2021 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

require "simplecov"

SimpleCov.profiles.define "canvas_rails" do
  add_filter "spec.rb"
  add_filter "/config/"
  add_filter "/spec_canvas/"
  add_filter "/db/"

  track_files "{app,config,lib,db}/**/*.rb"
  add_group "Controllers", "app/controllers"
  add_group "Models", "app/models"
  add_group "App", "app/"
  add_group "Gems", "gems/"
  add_group "Helpers", "app/helpers"
  add_group "Libraries", "lib/"
  add_group "Long files" do |src_file|
    src_file.lines.count > 500
  end
  add_group "Short files" do |src_file|
    src_file.lines.count <= 10
  end
end
