# frozen_string_literal: true

#
# Copyright (C) 2015 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.

require_relative "../../common"
require_relative "../../helpers/quizzes_common"
require_relative "../../helpers/assignment_overrides"

describe "viewing a quiz with variable due dates on the quizzes index page" do
  include_context "in-process server selenium tests"
  include QuizzesCommon
  include AssignmentOverridesSeleniumHelper

  context "as an observer linked to two students in different sections" do
    before(:once) { prepare_vdd_scenario_for_first_observer }

    before do
      skip("Entire spec context is buggy. Bug tickets created: CNVS-22794 and CNVS-22793")
      user_session(@observer1)
      get "/courses/#{@course.id}/quizzes"
    end

    it "shows the due dates for Section A", priority: "2" do
      skip("Bug ticket created: CNVS-22794")
      validate_vdd_quiz_tooltip_dates(
        ".date-due",
        "Everyone else\n#{format_date_for_view(@due_at_a)}"
      )
    end

    it "shows the due dates for Section B", priority: "2" do
      skip("Bug ticket created: CNVS-22794")
      validate_vdd_quiz_tooltip_dates(
        ".date-due",
        "#{@section_b.name}\n#{format_date_for_view(@due_at_b)}"
      )
    end

    it "shows the availability dates for Section A", priority: "2" do
      skip("Bug ticket created: CNVS-22793")
      validate_vdd_quiz_tooltip_dates(
        ".date-available",
        "Everyone else\nAvailable until #{format_date_for_view(@lock_at_a)}"
      )
    end

    it "shows the availability dates for Section B", priority: "2" do
      skip("Bug ticket created: CNVS-22793")
      validate_vdd_quiz_tooltip_dates(
        ".date-available",
        "#{@section_b.name}\nNot available until #{format_date_for_view(@unlock_at_b)}"
      )
    end
  end

  context "as an observer linked to a single student" do
    before(:once) { prepare_vdd_scenario_for_second_observer }

    before do
      user_session(@observer2)
      get "/courses/#{@course.id}/quizzes"
    end

    it "shows the due dates for Section B", priority: "2" do
      expect(f(".date-due")).to include_text("Due #{format_time_for_view(@due_at_b)}")
    end

    it "shows the availability dates for Section B", priority: "2" do
      expect(f(".date-available")).to include_text("Not available until " \
                                                   "#{format_date_for_view(@unlock_at_b, :short)}")
    end
  end
end
