# frozen_string_literal: true

#
# Copyright (C) 2015 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON><PERSON>ITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.

require_relative "../helpers/profile_common"

describe "profile_pics" do
  include_context "in-process server selenium tests"
  include_context "profile common"

  context "as a teacher" do
    before do
      course_with_teacher_logged_in
    end

    it_behaves_like "profile_settings_page"

    it_behaves_like "profile_user_about_page"

    it_behaves_like "user settings page change pic window"

    it_behaves_like "user settings change pic cancel"
  end
end
