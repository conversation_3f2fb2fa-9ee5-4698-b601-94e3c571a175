# frozen_string_literal: true

#
# Copyright (C) 2012 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.

require_relative "../common"

module SubmissionsCommon
  def create_assignment(type = "online_text_entry")
    assignment = @course.assignments.build({
                                             name: "media assignment",
                                             submission_types: type
                                           })
    assignment.workflow_state = "published"
    assignment.save!
    assignment
  end

  def create_assignment_and_go_to_page(type = "online_text_entry")
    assignment = create_assignment type
    get "/courses/#{@course.id}/assignments/#{assignment.id}"
    assignment
  end

  def comment_save_button
    fj('button:contains("Save")')
  end

  def create_text_file(file_path, content)
    File.write(file_path, content)
  end

  def upload_submission_comment_file
    Dir.mktmpdir do |tmpdir|
      # Create text files in the temp directory
      txt_file_1 = File.join(tmpdir, "file1.txt")
      create_text_file(txt_file_1, "This is the content of file1.")
      f(".attach_comment_file_link").click
      wait_for_ajaximations

      f('#add_comment_form input[type="file"]').send_keys(txt_file_1)
      wait_for_ajaximations
      expect(f("[data-testid='submission_comment_file_tag']")).to be_displayed
    end
  end

  def open_media_comment_dialog
    move_to_click(".media_comment_link")
    # swf and stuff loads, give it a sec to do its thing
    expect(f("#media_record_tabs")).to be_displayed
  end

  def submit_media_comment_1
    open_media_comment_dialog
    # pretend like we are flash sending data to the JS
    driver.execute_script <<~JS
      var entries1 = [{"duration":1.664,"thumbnailUrl":"http://www.instructuremedia.com/p/100/sp/10000/thumbnail/entry_id/0_jd6ger47/version/0","numComments":-1,"status":1,"rank":-1,"userScreenName":"_100_1_1","displayCredit":"_100_1_1","partnerLandingPage":null,"dataUrl":"http://www.instructuremedia.com/p/100/sp/10000/flvclipper/entry_id/0_jd6ger47/version/100000","sourceLink":"","subpId":10000,"puserId":"1_1","views":0,"height":0,"description":null,"hasThumbnail":false,"width":0,"kshowId":"0_pb7id2lf","kuserId":"","userLandingPage":"","mediaType":2,"plays":0,"partnerId":100,"adminTags":"","entryVersion":"","downloadUrl":"http://www.instructuremedia.com/p/100/sp/10000/raw/entry_id/0_jd6ger47/version/100000","createdAtDate":"1970-01-16T09:24:02.931Z","votes":-1,"uploaderName":null,"tags":"","entryName":"<EMAIL> 2012-02-21T16:48:37.729Z","entryType":1,"entryId":"0_jd6ger47","createdAtAsInt":1329842931,"uid":"E78A81CC-D03D-CD10-B449-A0D0D172EF38"}]
      mediaObjectCreated("0_jd6ger47", "video", "Media Contribution")
      addEntryComplete(entries1)
    JS
    wait_for_ajax_requests
  end

  def submit_media_comment_2
    open_media_comment_dialog
    driver.execute_script <<~JS
      var entries2 = [{"duration":1.829,"thumbnailUrl":"http://www.instructuremedia.com/p/100/sp/10000/thumbnail/entry_id/0_5hcd9mro/version/0","numComments":-1,"status":1,"rank":-1,"userScreenName":"_100_1_1","displayCredit":"_100_1_1","partnerLandingPage":null,"dataUrl":"http://www.instructuremedia.com/p/100/sp/10000/flvclipper/entry_id/0_5hcd9mro/version/100000","sourceLink":"","subpId":10000,"puserId":"1_1","views":0,"height":0,"description":null,"hasThumbnail":false,"width":0,"kshowId":"0_pb7id2lf","kuserId":"","userLandingPage":"","mediaType":2,"plays":0,"partnerId":100,"adminTags":"","entryVersion":"","downloadUrl":"http://www.instructuremedia.com/p/100/sp/10000/raw/entry_id/0_5hcd9mro/version/100000","createdAtDate":"1970-01-16T09:24:03.563Z","votes":-1,"uploaderName":null,"tags":"","entryName":"<EMAIL> 2012-02-21T16:59:11.249Z","entryType":1,"entryId":"0_5hcd9mro","createdAtAsInt":1329843563,"uid":"22A2C625-5FAB-AF3A-1A76-A0DA7572BFE4"}]
      mediaObjectCreated("0_5hcd9mro", "video", "Media Contribution")
      addEntryComplete(entries2)
    JS
    wait_for_ajax_requests
  end
end
