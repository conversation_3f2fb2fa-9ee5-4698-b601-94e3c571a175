# frozen_string_literal: true

#
# Copyright (C) 2018 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

RSpec.shared_context "cdn registry stubs" do
  before do
    allow(Canvas::Cdn).to receive(:registry).and_return(
      Canvas::Cdn::Registry.new(
        cache: Canvas::Cdn::Registry::StaticCache.new(
          gulp: {
            "fonts/lato/extended/Lato-Regular.woff2" => "mock_revved_url",
            "images/apple-touch-icon.png" => "images/apple-touch-icon-1234.png"
          },
          webpack: {
            "main" => "main-1234.js"
          }
        )
      )
    )
  end
end
