/*
 * Copyright (C) 2019 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

module.exports = {
  name: 'de_DE',
  day: {
    abbrev: ['So', '<PERSON>', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],
    full: ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag'],
  },
  month: {
    abbrev: ['Jan', 'Feb', '<PERSON><PERSON><PERSON>', 'Apr', '<PERSON>', 'Jun', 'Jul', 'Aug', 'Sept', 'Okt', 'Nov', 'Dez'],
    full: [
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      'April',
      'Mai',
      'Juni',
      'Juli',
      'August',
      'September',
      'Oktober',
      'November',
      'Dezember',
    ],
  },
  meridiem: ['', ''],
  date: '%d.%m.%Y',
  time24: '%T',
  dateTime: '%a %d %b %Y %T %Z',
  time12: '',
  full: '%a %-d. %b %H:%M:%S %Z %Y',
}
