/*
 * Copyright (C) 2023 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

module.exports = {
  name: 'ms_MY',
  day: {
    abbrev: ['Ahd', 'Isn', 'Sel', 'Rab', 'Kha', 'Jum', 'Sab'],
    full: ['Ahad', 'Isnin', '<PERSON><PERSON>a', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ma<PERSON>', 'Sabtu'],
  },
  month: {
    abbrev: ['Jan', 'Feb', '<PERSON>', 'Apr', '<PERSON>', 'Jun', 'Jul', '<PERSON><PERSON>', '<PERSON>', 'Okt', 'Nov', 'Dis'],
    full: [
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON>',
      'April',
      '<PERSON>',
      'Jun',
      'Julai',
      'Ogos',
      'September',
      'Oktober',
      'November',
      'Disember',
    ],
  },
  meridiem: ['PG', 'PTG'],
  date: '%d/%m/%Y',
  time24: '%H:%M:%S',
  dateTime: '%a %d %b %Y %T %Z',
  time12: '%I:%M:%S %p',
  full: '%a %d %b %Y %I:%M:%S %p %Z',
}
