/*
 * Copyright (C) 2019 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

module.exports = {
  name: 'pl_PL',
  day: {
    abbrev: ['nie', 'pon', 'wto', 'śro', 'czw', 'pią', 'sob'],
    full: ['niedziela', 'poniedziałek', 'wtorek', 'środa', 'czwartek', 'piątek', 'sobota'],
  },
  month: {
    abbrev: ['<PERSON><PERSON>', 'Lu<PERSON>', 'Mar', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', 'Lip', 'Sie', 'Wrz', '<PERSON>', 'Li<PERSON>', '<PERSON>ru'],
    full: [
      'styczeń',
      'luty',
      'marzec',
      'kwiecień',
      'maj',
      'czerwiec',
      'lipiec',
      'sierpień',
      'wrzesień',
      'październik',
      'listopad',
      'grudzień',
    ],
  },
  meridiem: ['', ''],
  date: '%d.%m.%Y',
  time24: '%T',
  dateTime: '%a, %-d %b %Y, %T',
  time12: '',
  full: '%a, %-d %b %Y, %T %Z',
}
