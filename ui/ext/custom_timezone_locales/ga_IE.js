/*
 * Copyright (C) 2024 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

module.exports = {
  name: 'ga_I<PERSON>',
  day: {
    abbrev: ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>oine', 'Sath'],
    full: [
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
    ],
  },
  month: {
    abbrev: [
      '<PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON>.<PERSON>.',
      '<PERSON>.<PERSON>.',
      '<PERSON>h',
      '<PERSON>ll',
    ],
    full: [
      '<PERSON>an<PERSON>ir',
      '<PERSON>abhra',
      '<PERSON><PERSON>rta',
      '<PERSON><PERSON><PERSON>',
      '<PERSON>al<PERSON>e',
      '<PERSON>theamh',
      '<PERSON><PERSON>il',
      '<PERSON><PERSON>asa',
      '<PERSON><PERSON> <PERSON><PERSON>mhair',
      '<PERSON>ireadh <PERSON><PERSON>mhair',
      '<PERSON>hain',
      '<PERSON>llaig',
    ],
  },
  meridiem: ['', ''],
  date: '%d/%m/%y',
  time24: '%T',
  dateTime: '%d %b %Y %T',
  time12: '',
  full: '%a %d %b %Y %T %Z',
}
