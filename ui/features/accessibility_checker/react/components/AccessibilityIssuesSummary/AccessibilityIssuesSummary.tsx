/*
 * Copyright (C) 2025 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */
import React, {useMemo} from 'react'
import {useShallow} from 'zustand/react/shallow'
import {Flex} from '@instructure/ui-flex'
import {View} from '@instructure/ui-view'

import {useAccessibilityCheckerStore} from '../../stores/AccessibilityCheckerStore'
import {
  USE_ACCESSIBILITY_SCANS_STORE,
  useAccessibilityScansStore,
} from '../../stores/AccessibilityScansStore'
import {calculateTotalIssuesCount, parseAccessibilityScans} from '../../utils/apiData'
import {IssuesByTypeChart} from './IssuesByTypeChart'
import {IssuesCounter} from './IssuesCounter'
import {AccessibilityData} from '../../types'

export const AccessibilityIssuesSummary = () => {
  const [accessibilityIssues, loading] = useAccessibilityCheckerStore(
    useShallow(state => [state.accessibilityIssues, state.loading]),
  )
  const [accessibilityScans, loadingN] = useAccessibilityScansStore(
    useShallow(state => [state.accessibilityScans, state.loading]),
  )

  const issues = useMemo(() => {
    if (USE_ACCESSIBILITY_SCANS_STORE) {
      return accessibilityScans
        ? parseAccessibilityScans(accessibilityScans)
        : ({} as AccessibilityData)
    }
    return accessibilityIssues
  }, [accessibilityScans, accessibilityIssues])

  const isLoading = USE_ACCESSIBILITY_SCANS_STORE ? loadingN : loading

  if (window.ENV.SCAN_DISABLED === true || isLoading) return null

  return (
    <Flex
      margin="medium 0 0 0"
      gap="small"
      alignItems="stretch"
      data-testid="accessibility-issues-summary"
    >
      <Flex.Item>
        <View as="div" padding="medium" borderWidth="small" borderRadius="medium" height="100%">
          <IssuesCounter count={calculateTotalIssuesCount(issues)} />
        </View>
      </Flex.Item>
      <Flex.Item shouldGrow shouldShrink>
        <View as="div" padding="x-small" borderWidth="small" borderRadius="medium" height="100%">
          <IssuesByTypeChart accessibilityIssues={issues} isLoading={isLoading} />
        </View>
      </Flex.Item>
    </Flex>
  )
}
