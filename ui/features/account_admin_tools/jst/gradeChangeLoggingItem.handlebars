<td>
  {{tDateToString created_at "medium"}}
</td>
<td>
  {{tTimeToString created_at "tiny"}}
</td>
{{#ifEqual event_type 'corrupted'}}
<td colspan="6">
  {{#t "event_type.corrupted"}}Details Not Available{{/t}}
</td>
{{else}}
<td>
  {{#if excused_before}}
    {{#t}}EX{{/t}}
  {{else}}
    {{#if grade_before}}
      {{grade_before}}
    {{else}}
      {{#t "blank_placeholder"}}-{{/t}}
    {{/if}}
  {{/if}}
</td>
<td>
  {{#if excused_after}}
    {{#t}}EX{{/t}}
  {{else}}
    {{#if grade_after}}
      {{grade_after}}
    {{else}}
      {{#t "blank_placeholder"}}-{{/t}}
    {{/if}}
  {{/if}}
</td>
<td>
  {{#if grader}}
    {{grader.name}}
  {{else}}
    {{#t "blank_placeholder"}}-{{/t}}
  {{/if}}
</td>
<td>
  {{#if student}}
    {{student.name}}
  {{else}}
    {{#t "blank_placeholder"}}-{{/t}}
  {{/if}}
</td>
<td>
  {{#if course}}
    {{course.name}}
  {{else}}
    {{#t "blank_placeholder"}}-{{/t}}
  {{/if}}
</td>
<td>
  {{#if assignment}}
    {{assignment.name}}
  {{else}}
    {{#t "blank_placeholder"}}-{{/t}}
  {{/if}}
</td>
<td>
  {{#ifNull graded_anonymously}}
    {{#t "blank_placeholder"}}-{{/t}}
  {{else}}
    {{#if graded_anonymously}}
      {{#t "yes-abbreviation"}}y{{/t}}
    {{else}}
      {{#t "no-abbreviation"}}n{{/t}}
    {{/if}}
  {{/ifNull}}
</td>
{{/ifEqual}}
