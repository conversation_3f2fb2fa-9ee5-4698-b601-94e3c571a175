/*
 * Copyright (C) 2024 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

.acceptableUsePolicy {
  hyphens: auto;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

/*
 * the following styles have been adapted from the body styles in
 * app/stylesheets/components/_ic-typography.scss to ensure visual consistency
 * for AUP content by aligning the typography of the new React-powered AUP page
 * with that of the previous RoR/ERB template styling
 */
.acceptableUsePolicy__content {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  color: var(--ic-brand-font-color-dark);
  font-size: 1rem;
  font-weight: normal;
  line-height: 1.5;
}

.acceptableUsePolicy__content > *:first-child {
  margin-top: 0;
}

.acceptableUsePolicy__content > *:last-child {
  margin-bottom: 0;
}
