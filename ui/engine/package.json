{"name": "@canvas/engine", "version": "1.0.0", "description": "Provides boot-up capabilities to front-end bundles", "private": true, "type": "module", "exports": {".": "./index.ts"}, "dependencies": {"@instructure/updown": "^1.3", "@formatjs/intl-getcanonicallocales": "^2", "@formatjs/intl-locale": "^3", "@formatjs/intl-pluralrules": "^5", "@formatjs/intl-datetimeformat": "^6", "@formatjs/intl-numberformat": "^8", "@formatjs/intl-relativetimeformat": "^11"}}